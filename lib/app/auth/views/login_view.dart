import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:techrar_gym/app/auth/providers/auth_provider.dart';
import 'package:techrar_gym/core/theme/app_colors.dart';
import 'package:techrar_gym/core/theme/decorations.dart';
import '../providers/login_provider.dart';
import '../models/login_view_state.dart';
import 'forgot_password_view.dart';

class LoginView extends ConsumerStatefulWidget {
  static final name = 'login-view';
  const LoginView({super.key});

  @override
  ConsumerState<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends ConsumerState<LoginView> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final loginNotifier = ref.watch(loginNotifierProvider.notifier);
    final loginState = ref.watch(loginNotifierProvider);

    // Listen to auth state changes
    ref.listen(authNotifierProvider, (previous, next) {
      if (next.hasValue && next.value == true) {
        // Login successful - navigation will be handled by AuthGard
      }
    });

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpacing.screenPadding),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: AppSpacing.xxxl),

                // Logo/Title Section
                _buildHeader(),

                const SizedBox(height: AppSpacing.xxl),

                // Email Field
                _buildEmailField(loginNotifier, loginState),

                const SizedBox(height: AppSpacing.md),

                // Password Field
                _buildPasswordField(loginNotifier, loginState),

                const SizedBox(height: AppSpacing.sm),

                // Forgot Password
                _buildForgotPasswordLink(),

                const SizedBox(height: AppSpacing.xl),

                // General Error Message
                if (loginState.generalError != null) ...[
                  _buildErrorMessage(loginState.generalError!),
                  const SizedBox(height: AppSpacing.md),
                ],

                // Login Button
                _buildLoginButton(loginNotifier, loginState),

                const SizedBox(height: AppSpacing.lg),

                // Sign Up Link (if needed)
                _buildSignUpLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // App Logo/Icon
        Container(
          width: AppSizes.avatarXl,
          height: AppSizes.avatarXl,
          decoration: BoxDecoration(
            color: AppColors.primaryColor,
            borderRadius: BorderRadius.circular(AppSizes.radiusLg),
            boxShadow: Styles.floatingShadow,
          ),
          child: Icon(
            Icons.fitness_center,
            color: AppColors.white,
            size: AppSizes.iconXl,
          ),
        ),
        const SizedBox(height: AppSpacing.lg),

        // Welcome Text
        Text(
          'Welcome Back!',
          style: TextStyles.t1.copyWith(color: AppColors.primaryTextColor),
        ),
        const SizedBox(height: AppSpacing.sm),
        Text(
          'Sign in to continue to your gym account',
          style: TextStyles.body1.copyWith(color: AppColors.secondaryTextColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildEmailField(LoginNotifier notifier, LoginViewState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email',
          style: TextStyles.inputLabel,
        ),
        const SizedBox(height: AppSpacing.sm),
        TextFormField(
          controller: _emailController,
          keyboardType: TextInputType.emailAddress,
          textInputAction: TextInputAction.next,
          onChanged: notifier.updateEmail,
          style: TextStyles.inputText,
          decoration: InputDecoration(
            hintText: 'Enter your email',
            hintStyle: TextStyles.inputHint,
            prefixIcon: Icon(Icons.email_outlined, color: AppColors.secondaryTextColor),
            filled: true,
            fillColor: AppColors.inputBackgroundColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputBorderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputBorderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputFocusedBorderColor, width: AppSizes.inputFocusedBorderWidth),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputErrorBorderColor, width: AppSizes.inputFocusedBorderWidth),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputErrorBorderColor, width: AppSizes.inputFocusedBorderWidth),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.md),
            errorText: state.emailError,
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField(LoginNotifier notifier, LoginViewState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password',
          style: TextStyles.inputLabel,
        ),
        const SizedBox(height: AppSpacing.sm),
        TextFormField(
          controller: _passwordController,
          obscureText: state.obscurePassword,
          textInputAction: TextInputAction.done,
          onChanged: notifier.updatePassword,
          onFieldSubmitted: (_) => _handleLogin(notifier, state),
          style: TextStyles.inputText,
          decoration: InputDecoration(
            hintText: 'Enter your password',
            hintStyle: TextStyles.inputHint,
            prefixIcon: Icon(Icons.lock_outline, color: AppColors.secondaryTextColor),
            suffixIcon: IconButton(
              icon: Icon(
                state.obscurePassword ? Icons.visibility_off : Icons.visibility,
                color: AppColors.secondaryTextColor,
              ),
              onPressed: notifier.togglePasswordVisibility,
            ),
            filled: true,
            fillColor: AppColors.inputBackgroundColor,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputBorderColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputBorderColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputFocusedBorderColor, width: AppSizes.inputFocusedBorderWidth),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputErrorBorderColor, width: AppSizes.inputFocusedBorderWidth),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppSizes.inputRadius),
              borderSide: BorderSide(color: AppColors.inputErrorBorderColor, width: AppSizes.inputFocusedBorderWidth),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: AppSpacing.md, vertical: AppSpacing.md),
            errorText: state.passwordError,
          ),
        ),
      ],
    );
  }

  Widget _buildForgotPasswordLink() {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const ForgotPasswordView(),
            ),
          );
        },
        child: Text(
          'Forgot Password?',
          style: TextStyles.body2.copyWith(
            color: AppColors.primaryColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.sm),
      decoration: BoxDecoration(
        color: AppColors.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppSizes.radiusSm),
        border: Border.all(color: AppColors.errorColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.errorColor,
            size: AppSizes.iconSm,
          ),
          const SizedBox(width: AppSpacing.sm),
          Expanded(
            child: Text(
              message,
              style: TextStyles.errorText,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginButton(LoginNotifier notifier, LoginViewState state) {
    return SizedBox(
      width: double.infinity,
      height: AppSizes.buttonHeight,
      child: ElevatedButton(
        onPressed: state.isLoading ? null : () => _handleLogin(notifier, state),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.buttonPrimaryColor,
          foregroundColor: AppColors.buttonTextColor,
          disabledBackgroundColor: AppColors.buttonDisabledColor,
          elevation: AppSizes.appBarElevation,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.buttonRadius),
          ),
        ),
        child: state.isLoading
            ? SizedBox(
                width: AppSizes.iconMd,
                height: AppSizes.iconMd,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                ),
              )
            : Text(
                'Sign In',
                style: TextStyles.buttonText,
              ),
      ),
    );
  }

  Widget _buildSignUpLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "Don't have an account? ",
          style: TextStyles.body2.copyWith(color: AppColors.secondaryTextColor),
        ),
        TextButton(
          onPressed: () {
            // TODO: Navigate to sign up screen
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('Sign up feature coming soon!'),
                backgroundColor: AppColors.infoColor,
              ),
            );
          },
          child: Text(
            'Sign Up',
            style: TextStyles.body2.copyWith(
              color: AppColors.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  void _handleLogin(LoginNotifier notifier, LoginViewState state) {
    // Update controllers to match state
    if (_emailController.text != state.email) {
      _emailController.text = state.email;
    }
    if (_passwordController.text != state.password) {
      _passwordController.text = state.password;
    }

    // Trigger login
    notifier.login();
  }
}
