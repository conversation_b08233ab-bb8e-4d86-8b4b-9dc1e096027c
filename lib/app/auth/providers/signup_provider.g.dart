// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signup_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$signupNotifierHash() => r'a33f8e08e023efa663cbf04aa7a9b817a4d60dbe';

/// See also [SignupNotifier].
@ProviderFor(SignupNotifier)
final signupNotifierProvider =
    AutoDisposeNotifierProvider<SignupNotifier, SignupViewState>.internal(
  SignupNotifier.new,
  name: r'signupNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$signupNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignupNotifier = AutoDisposeNotifier<SignupViewState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
